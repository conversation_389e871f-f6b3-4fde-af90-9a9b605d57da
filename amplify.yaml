version: 1
applications:
    -
        backend:
            phases:
                preBuild:
                    commands:
                        - 'npm install -g pnpm'
                        - 'pnpm install'
                build:
                    commands:
                        - 'npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID'
                        - 'cp apps/backend/amplify_outputs.json apps/web/amplify_outputs.json'
        frontend:
            phases:
                preBuild:
                    commands:
                        - 'npm install -g pnpm'
                        - 'pnpm install'
                build:
                    commands: ['npx turbo run build --filter=@convx/web']
            artifacts:
                baseDirectory: apps/web/dist
                files:
                    - '**/*'
            cache:
                paths:
                    - 'node_modules/**/*'
            buildPath: /
        appRoot: apps/web

version: 1
applications:
    -
        frontend:
            phases:
                preBuild:
                    commands:
                        - npm install -g pnpm@10.12.3
                        - pnpm install --frozen-lockfile
                        # Copy Amplify outputs to web app (with fallback)
                        - |
                          mkdir -p src
                          if [ -f "../backend/amplify_outputs.json" ]; then
                            cp ../backend/amplify_outputs.json src/amplify_outputs.json
                            echo "Successfully copied amplify_outputs.json"
                          elif [ -f "../../amplify_outputs.json" ]; then
                            cp ../../amplify_outputs.json src/amplify_outputs.json
                            echo "Successfully copied amplify_outputs.json from root"
                          else
                            echo "Warning: amplify_outputs.json not found, creating placeholder"
                            echo '{"version":"1.4","auth":{"aws_region":"us-east-1","user_pool_id":"","user_pool_client_id":"","identity_pool_id":""},"data":{}}' > src/amplify_outputs.json
                          fi
                build:
                    commands: ['pnpm run build']
            artifacts:
                baseDirectory: apps/web/dist
                files:
                    - '**/*'
            cache:
                paths:
                    - 'node_modules/**/*'
            buildPath: /
        appRoot: apps/web

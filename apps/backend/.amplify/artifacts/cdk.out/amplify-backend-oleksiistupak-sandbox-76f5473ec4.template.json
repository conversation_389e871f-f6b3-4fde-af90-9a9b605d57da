{"Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"AmplifySandbox\",\"createdWith\":\"1.16.1\",\"stackType\":\"root\",\"metadata\":{}}", "Metadata": {"AWS::Amplify::Platform": {"version": "1", "stackOutputs": ["deploymentType", "region"]}, "AWS::Amplify::Auth": {"version": "1", "stackOutputs": ["userPoolId", "webClientId", "identityPoolId", "authRegion", "allowUnauthenticatedIdentities", "signupAttributes", "usernameAttributes", "verificationMechanisms", "passwordPolicyMinLength", "passwordPolicyRequirements", "mfaConfiguration", "mfaTypes", "socialProviders", "oauthCognitoDomain", "oauthScope", "oauthRedirectSignIn", "oauthRedirectSignOut", "oauthResponseType", "oauthClientId", "groups"]}, "AWS::Amplify::GraphQL": {"version": "1", "stackOutputs": ["awsAppsyncApiId", "awsAppsyncApiEndpoint", "awsAppsyncAuthenticationType", "awsAppsyncRegion", "amplifyApiModelSchemaS3Uri", "awsAppsyncAdditionalAuthenticationTypes"]}}, "Outputs": {"deploymentType": {"Value": "sandbox"}, "region": {"Value": {"Ref": "AWS::Region"}}, "userPoolId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"]}}, "webClientId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref"]}}, "identityPoolId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"]}}, "authRegion": {"Value": {"Ref": "AWS::Region"}}, "allowUnauthenticatedIdentities": {"Value": "true"}, "signupAttributes": {"Value": "[\"email\"]"}, "usernameAttributes": {"Value": "[\"email\"]"}, "verificationMechanisms": {"Value": "[\"email\"]"}, "passwordPolicyMinLength": {"Value": "8"}, "passwordPolicyRequirements": {"Value": "[\"REQUIRES_NUMBERS\",\"REQUIRES_LOWERCASE\",\"REQUIRES_UPPERCASE\",\"REQUIRES_SYMBOLS\"]"}, "mfaConfiguration": {"Value": "OFF"}, "mfaTypes": {"Value": "[]"}, "socialProviders": {"Value": ""}, "oauthCognitoDomain": {"Value": ""}, "oauthScope": {"Value": "[\"profile\",\"phone\",\"email\",\"openid\",\"aws.cognito.signin.user.admin\"]"}, "oauthRedirectSignIn": {"Value": "https://example.com"}, "oauthRedirectSignOut": {"Value": ""}, "oauthResponseType": {"Value": "code"}, "oauthClientId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref"]}}, "groups": {"Value": "[]"}, "awsAppsyncApiId": {"Value": {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"]}}, "awsAppsyncApiEndpoint": {"Value": {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl"]}}, "awsAppsyncAuthenticationType": {"Value": "AWS_IAM"}, "awsAppsyncRegion": {"Value": {"Ref": "AWS::Region"}}, "amplifyApiModelSchemaS3Uri": {"Value": {"Fn::Join": ["", ["s3://", {"Fn::Select": [0, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn"]}]}]}]}]}, "/model-schema.graphql"]]}}, "awsAppsyncAdditionalAuthenticationTypes": {"Value": "AMAZON_COGNITO_USER_POOLS"}}, "Resources": {"auth179371D7": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/f929ef4a2a55bb95cd3541d12fb0df824b096a26b2e344744338a56bea560c37.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth.NestedStack/auth.NestedStackResource", "aws:asset:path": "amplifybackendoleksiistupaksandbox76f5473ec4authC9BD36A0.nested.template.json", "aws:asset:property": "TemplateURL"}}, "data7552DF31": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"]}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/fd82bae47151d806d4672f457cbc91f303ac948c35b2613e314faa47c24fd5e5.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data.NestedStack/data.NestedStackResource", "aws:asset:path": "amplifybackendoleksiistupaksandbox76f5473ec4dataF72EB429.nested.template.json", "aws:asset:property": "TemplateURL"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/zPSM7Sw1DNUTCwv1k1OydbNyUzSqw4uSUzO1nFOy/MvLSkoLQGxwEK1Onn5Kal6WcX6ZUZGeoZmegaKWcWZmbpFpXklmbmpekEQGgD0zniXVAAAAA=="}, "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}
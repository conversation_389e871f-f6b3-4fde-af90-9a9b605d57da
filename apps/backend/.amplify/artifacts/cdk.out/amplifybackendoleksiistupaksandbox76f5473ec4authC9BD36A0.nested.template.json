{"Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"AmplifySandbox\",\"createdWith\":\"1.8.1\",\"stackType\":\"auth-Cognito\",\"metadata\":{}}", "Resources": {"amplifyAuthUserPool4BA7F805": {"Type": "AWS::Cognito::UserPool", "Properties": {"AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "AutoVerifiedAttributes": ["email"], "EmailVerificationMessage": "The verification code to your new account is {####}", "EmailVerificationSubject": "Verify your new account", "Policies": {"PasswordPolicy": {"MinimumLength": 8, "RequireLowercase": true, "RequireNumbers": true, "RequireSymbols": true, "RequireUppercase": true}}, "Schema": [{"Mutable": true, "Name": "email", "Required": true}], "SmsVerificationMessage": "The verification code to your new account is {####}", "UserAttributeUpdateSettings": {"AttributesRequireVerificationBeforeUpdate": ["email"]}, "UserPoolTags": {"amplify:deployment-type": "sandbox", "amplify:friendly-name": "amplifyAuth", "created-by": "amplify"}, "UsernameAttributes": ["email"], "UsernameConfiguration": {"CaseSensitive": false}, "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailMessage": "The verification code to your new account is {####}", "EmailSubject": "Verify your new account", "SmsMessage": "The verification code to your new account is {####}"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPool/Resource"}}, "amplifyAuthUserPoolAppClient2626C6F8": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"AllowedOAuthFlows": ["code"], "AllowedOAuthFlowsUserPoolClient": true, "AllowedOAuthScopes": ["profile", "phone", "email", "openid", "aws.cognito.signin.user.admin"], "CallbackURLs": ["https://example.com"], "ExplicitAuthFlows": ["ALLOW_CUSTOM_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"], "PreventUserExistenceErrors": "ENABLED", "SupportedIdentityProviders": ["COGNITO"], "UserPoolId": {"Ref": "amplifyAuthUserPool4BA7F805"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPoolAppClient/Resource"}}, "amplifyAuthIdentityPool3FDE84CC": {"Type": "AWS::Cognito::IdentityPool", "Properties": {"AllowUnauthenticatedIdentities": true, "CognitoIdentityProviders": [{"ClientId": {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}, "ProviderName": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}]]}}], "IdentityPoolTags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyAuth"}, {"Key": "created-by", "Value": "amplify"}], "SupportedLoginProviders": {}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPool"}}, "amplifyAuthauthenticatedUserRoleD8DA3689": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "authenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyAuth"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/authenticatedUserRole/Resource"}}, "amplifyAuthunauthenticatedUserRole2B524D9E": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "unauthenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyAuth"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/unauthenticatedUserRole/Resource"}}, "amplifyAuthIdentityPoolRoleAttachment045F17C8": {"Type": "AWS::Cognito::IdentityPoolRoleAttachment", "Properties": {"IdentityPoolId": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}, "RoleMappings": {"UserPoolWebClientRoleMapping": {"AmbiguousRoleResolution": "AuthenticatedRole", "IdentityProvider": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}, ":", {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}]]}, "Type": "Token"}}, "Roles": {"unauthenticated": {"Fn::GetAtt": ["amplifyAuthunauthenticatedUserRole2B524D9E", "<PERSON><PERSON>"]}, "authenticated": {"Fn::GetAtt": ["amplifyAuthauthenticatedUserRoleD8DA3689", "<PERSON><PERSON>"]}}}, "DependsOn": ["amplifyAuthIdentityPool3FDE84CC", "amplifyAuthUserPoolAppClient2626C6F8"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPoolRoleAttachment"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/02LwQ6CMBBEv4V7WYWD0aPh5MUYjGdT21UXSjehi8Q0/XdDDMppZt7MlFBsd1Bkegy5sW3u6AbxiEHQnkWbVukxXKPhhydhiJeA/YnZqeruf342lSP0sqz+5GDRC8l7Pi9zzQ73Ito8O/SSFOkO4gSn4aQpqRoDD73BpDxbhCasXmUJxQbWWROI8n7wQh1C/dUP5hIwGdcAAAA="}, "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Outputs": {"amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"Value": {"Ref": "amplifyAuthUserPool4BA7F805"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref": {"Value": {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Value": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Value": {"Ref": "amplifyAuthauthenticatedUserRoleD8DA3689"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Value": {"Ref": "amplifyAuthunauthenticatedUserRole2B524D9E"}}}}
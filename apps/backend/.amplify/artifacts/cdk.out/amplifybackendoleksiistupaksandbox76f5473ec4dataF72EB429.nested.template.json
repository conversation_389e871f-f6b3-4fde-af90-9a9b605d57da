{"Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"AmplifySandbox\",\"createdWith\":\"1.20.3\",\"stackType\":\"api-AppSync\",\"metadata\":{\"dataSources\":\"dynamodb\",\"authorizationModes\":\"amazon_cognito_identity_pools,amazon_cognito_user_pools,aws_iam\",\"customOperations\":\"\"}}", "Resources": {"amplifyDataGraphQLAPI42A6FA33": {"Type": "AWS::AppSync::GraphQLApi", "Properties": {"AdditionalAuthenticationProviders": [{"AuthenticationType": "AMAZON_COGNITO_USER_POOLS", "UserPoolConfig": {"AwsRegion": {"Ref": "AWS::Region"}, "UserPoolId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"}}}], "AuthenticationType": "AWS_IAM", "Name": "amplifyData", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/Resource"}}, "amplifyDataGraphQLAPITransformerSchemaFF50A789": {"Type": "AWS::AppSync::GraphQLSchema", "Properties": {"ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "DefinitionS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/5d06176bcc3e592a227be94f877d286eb82f30e2066d478a8eeb091baf786836.graphql"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/TransformerSchema"}}, "amplifyDataGraphQLAPINONEDS684BF699": {"Type": "AWS::AppSync::DataSource", "Properties": {"ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "Description": "None Data Source for Pipeline functions", "Name": "NONE_DS", "Type": "NONE"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/NONE_DS/Resource"}}, "amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/3c9bb4e8e9f3c54bb81bc0f237483dfcca03b0cb04612c1a2d76dde770e7a72d.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource", "aws:asset:path": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManager5F62C3C0.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataTodoNestedStackTodoNestedStackResource551CEA56": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputsamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableMana3FE0897F": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/80161cf112a33442f6fa4d89279b689d0162b640ef57182576f71ee1042978e8.json"]]}}, "DependsOn": ["amplifyDataGraphQLAPITransformerSchemaFF50A789"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo.NestedStack/Todo.NestedStackResource", "aws:asset:path": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataTodo2ED73CFF.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataAuthRolePolicy01567A5654": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"Description": "", "Path": "/", "PolicyDocument": {"Statement": [{"Action": "appsync:GraphQL", "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/*", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Todo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "getTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "listTodos"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "createTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "updateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "deleteTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onCreateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onUpdateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onDeleteTodo"}]}]}], "Version": "2012-10-17"}, "Roles": [{"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AuthRolePolicy01/Resource"}}, "amplifyDataUnauthRolePolicy01355B9DCF": {"Type": "AWS::IAM::ManagedPolicy", "Properties": {"Description": "", "Path": "/", "PolicyDocument": {"Statement": [{"Action": "appsync:GraphQL", "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/*", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Todo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "getTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "listTodos"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "createTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "updateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "deleteTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onCreateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onUpdateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onDeleteTodo"}]}]}], "Version": "2012-10-17"}, "Roles": [{"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/UnauthRolePolicy01/Resource"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"CorsConfiguration": {"CorsRules": [{"AllowedHeaders": ["*"], "AllowedMethods": ["GET", "HEAD"], "AllowedOrigins": [{"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/amplify"]]}]}]}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "aws-cdk:auto-delete-objects", "Value": "true"}, {"Key": "aws-cdk:cr-owned:6628fe5e", "Value": "true"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "PolicyDocument": {"Statement": [{"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketAutoDeleteObjectsCustomResource437F26F5": {"Type": "Custom::S3AutoDeleteObjects", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F", "<PERSON><PERSON>"]}, "BucketName": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}}, "DependsOn": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"Content": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip"}, "Description": "/opt/awscli/aws"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource", "aws:asset:path": "asset.c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip", "aws:asset:is-bundled": false, "aws:asset:property": "Content"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929": {"Type": "Custom::CDKBucketDeployment", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21", "<PERSON><PERSON>"]}, "SourceBucketNames": [{"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}], "SourceObjectKeys": ["76b9cb787843795ad95966b867f6ef348480676166e6ac8fd4d53cef654a6b16.zip"], "SourceMarkers": [{}], "DestinationBucketName": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "Prune": true, "OutputObjectKeys": true, "DestinationBucketArn": {"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "index.handler", "Role": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}, "Runtime": {"Fn::FindInMap": ["LatestNodeRuntimeMap", {"Ref": "AWS::Region"}, "value"]}, "Description": {"Fn::Join": ["", ["Lambda function for auto-deleting objects in ", {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, " S3 bucket."]]}}, "DependsOn": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "aws:asset:path": "asset.faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6", "aws:asset:property": "Code"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}]]}, {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "Roles": [{"Ref": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f.zip"}, "Environment": {"Variables": {"AWS_CA_BUNDLE": "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"}}, "Handler": "index.handler", "Layers": [{"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "MemorySize": 1536, "Role": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2", "<PERSON><PERSON>"]}, "Runtime": "python3.11", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "Timeout": 900}, "DependsOn": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource", "aws:asset:path": "asset.4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "modelIntrospectionSchemaBucketF566B665": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "aws-cdk:auto-delete-objects", "Value": "true"}, {"Key": "aws-cdk:cr-owned:a1ebbbad", "Value": "true"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Resource"}}, "modelIntrospectionSchemaBucketPolicy4DAB0D15": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "PolicyDocument": {"Statement": [{"Action": "s3:*", "Condition": {"Bool": {"aws:SecureTransport": "false"}}, "Effect": "<PERSON><PERSON>", "Principal": {"AWS": "*"}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Policy/Resource"}}, "modelIntrospectionSchemaBucketAutoDeleteObjectsCustomResourceFE57309F": {"Type": "Custom::S3AutoDeleteObjects", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F", "<PERSON><PERSON>"]}, "BucketName": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}, "DependsOn": ["modelIntrospectionSchemaBucketPolicy4DAB0D15"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default"}}, "modelIntrospectionSchemaBucketDeploymentAwsCliLayer13C432F7": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"Content": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip"}, "Description": "/opt/awscli/aws"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource", "aws:asset:path": "asset.c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip", "aws:asset:is-bundled": false, "aws:asset:property": "Content"}}, "modelIntrospectionSchemaBucketDeploymentCustomResource1536MiB104B97EC": {"Type": "Custom::CDKBucketDeployment", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21", "<PERSON><PERSON>"]}, "SourceBucketNames": [{"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}], "SourceObjectKeys": ["8aeb4ca549a33370c42666525e2c318f84f0d26a7f54c6a8ecc60ce32b83123f.zip"], "SourceMarkers": [{}], "DestinationBucketName": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "Prune": true, "OutputObjectKeys": true}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default"}}, "AMPLIFYDATAGRAPHQLENDPOINTParameter1C2CBB16": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_GRAPHQL_ENDPOINT", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource"}}, "AMPLIFYDATAMODELINTROSPECTIONSCHEMABUCKETNAMEParameter47BF4F44": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAME", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource"}}, "AMPLIFYDATAMODELINTROSPECTIONSCHEMAKEYParameterB6AEAE8A": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEY", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": "modelIntrospectionSchema.json"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource"}}, "AMPLIFYDATADEFAULTNAMEParameterE7C23CC4": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_DEFAULT_NAME", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": "amplifyData"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/1VRy26DMBD8lt4dt0mlqj0SovaSRmmQekUbsxAnxqZeE4Qs/r0ybkg5zcPr1Xi84svXN758gI4WorgslDxyv0NyWGQOxIUdkExrBTLoKPfQNNRrwf2Hheb0o5JGroGQpaUena9t0sh/KhMnrIHtjMYNOMjiqrTUdzUwCTX3n6ChwmJvlBR9mJgbB6PGeyPehyIbGD1zv27FBV1w/1iE+/BMRxHCh9u5L7BRpq9ROx6PNpPBgAgd8STAwBTUxwK4T0u9hR7tN1qSRrNM6kqhM/q91cIFZyJpOZkDI6q5z5yVutqDhRod2vEtNzEMQcb605acqadPCA3ceMyRq5Ahh46EkjzpKFVyjMXGuJmDSupqtj+22zSjP9u/t+YqC7SxFm0K5Gd6vK5WfPnCnx7OJOXCttrJGvkh4i+8AkGqQQIAAA=="}, "Metadata": {"aws:cdk:path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Parameters": {"DynamoDBModelTableReadIOPS": {"Type": "Number", "Default": 5, "Description": "The number of read IOPS the table should support."}, "DynamoDBModelTableWriteIOPS": {"Type": "Number", "Default": 5, "Description": "The number of write IOPS the table should support."}, "DynamoDBBillingMode": {"Type": "String", "Default": "PAY_PER_REQUEST", "AllowedValues": ["PAY_PER_REQUEST", "PROVISIONED"], "Description": "Configure @model types to create DynamoDB tables with PAY_PER_REQUEST or PROVISIONED billing modes."}, "DynamoDBEnablePointInTimeRecovery": {"Type": "String", "Default": "false", "AllowedValues": ["true", "false"], "Description": "Whether to enable Point in Time Recovery on the table."}, "DynamoDBEnableServerSideEncryption": {"Type": "String", "Default": "true", "AllowedValues": ["true", "false"], "Description": "Enable server side encryption powered by KMS."}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"Type": "String"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Type": "String"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Type": "String"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Type": "String"}}, "Mappings": {"LatestNodeRuntimeMap": {"af-south-1": {"value": "nodejs20.x"}, "ap-east-1": {"value": "nodejs20.x"}, "ap-northeast-1": {"value": "nodejs20.x"}, "ap-northeast-2": {"value": "nodejs20.x"}, "ap-northeast-3": {"value": "nodejs20.x"}, "ap-south-1": {"value": "nodejs20.x"}, "ap-south-2": {"value": "nodejs20.x"}, "ap-southeast-1": {"value": "nodejs20.x"}, "ap-southeast-2": {"value": "nodejs20.x"}, "ap-southeast-3": {"value": "nodejs20.x"}, "ap-southeast-4": {"value": "nodejs20.x"}, "ap-southeast-5": {"value": "nodejs20.x"}, "ap-southeast-7": {"value": "nodejs20.x"}, "ca-central-1": {"value": "nodejs20.x"}, "ca-west-1": {"value": "nodejs20.x"}, "cn-north-1": {"value": "nodejs20.x"}, "cn-northwest-1": {"value": "nodejs20.x"}, "eu-central-1": {"value": "nodejs20.x"}, "eu-central-2": {"value": "nodejs20.x"}, "eu-isoe-west-1": {"value": "nodejs18.x"}, "eu-north-1": {"value": "nodejs20.x"}, "eu-south-1": {"value": "nodejs20.x"}, "eu-south-2": {"value": "nodejs20.x"}, "eu-west-1": {"value": "nodejs20.x"}, "eu-west-2": {"value": "nodejs20.x"}, "eu-west-3": {"value": "nodejs20.x"}, "il-central-1": {"value": "nodejs20.x"}, "me-central-1": {"value": "nodejs20.x"}, "me-south-1": {"value": "nodejs20.x"}, "mx-central-1": {"value": "nodejs20.x"}, "sa-east-1": {"value": "nodejs20.x"}, "us-east-1": {"value": "nodejs20.x"}, "us-east-2": {"value": "nodejs20.x"}, "us-gov-east-1": {"value": "nodejs20.x"}, "us-gov-west-1": {"value": "nodejs20.x"}, "us-iso-east-1": {"value": "nodejs18.x"}, "us-iso-west-1": {"value": "nodejs18.x"}, "us-isob-east-1": {"value": "nodejs18.x"}, "us-west-1": {"value": "nodejs20.x"}, "us-west-2": {"value": "nodejs20.x"}}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Outputs": {"amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl": {"Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn": {"Value": {"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929", "DestinationBucketArn"]}}}}
type Todo {
  content: String
  id: ID!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

input AmplifyAIConversationTurnErrorInput {
  errorType: String!
  message: String!
}

input ModelStringInput {
  ne: String
  eq: String
  le: String
  lt: String
  ge: String
  gt: String
  contains: String
  notContains: String
  between: [String]
  beginsWith: String
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
  size: ModelSizeInput
}

input ModelIntInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelFloatInput {
  ne: Float
  eq: Float
  le: Float
  lt: Float
  ge: Float
  gt: Float
  between: [Float]
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelBooleanInput {
  ne: Boolean
  eq: Boolean
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelIDInput {
  ne: ID
  eq: ID
  le: ID
  lt: ID
  ge: ID
  gt: ID
  contains: ID
  notContains: ID
  between: [ID]
  beginsWith: ID
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
  size: ModelSizeInput
}

input ModelSubscriptionStringInput {
  ne: String
  eq: String
  le: String
  lt: String
  ge: String
  gt: String
  contains: String
  notContains: String
  between: [String]
  beginsWith: String
  in: [String]
  notIn: [String]
}

input ModelSubscriptionIntInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
  in: [Int]
  notIn: [Int]
}

input ModelSubscriptionFloatInput {
  ne: Float
  eq: Float
  le: Float
  lt: Float
  ge: Float
  gt: Float
  between: [Float]
  in: [Float]
  notIn: [Float]
}

input ModelSubscriptionBooleanInput {
  ne: Boolean
  eq: Boolean
}

input ModelSubscriptionIDInput {
  ne: ID
  eq: ID
  le: ID
  lt: ID
  ge: ID
  gt: ID
  contains: ID
  notContains: ID
  between: [ID]
  beginsWith: ID
  in: [ID]
  notIn: [ID]
}

enum ModelAttributeTypes {
  binary
  binarySet
  bool
  list
  map
  number
  numberSet
  string
  stringSet
  _null
}

input ModelSizeInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
}

enum ModelSortDirection {
  ASC
  DESC
}

type ModelTodoConnection {
  items: [Todo]!
  nextToken: String
}

input ModelTodoFilterInput {
  content: ModelStringInput
  id: ModelIDInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  and: [ModelTodoFilterInput]
  or: [ModelTodoFilterInput]
  not: ModelTodoFilterInput
}

type Query {
  getTodo(id: ID!): Todo
  listTodos(filter: ModelTodoFilterInput, limit: Int, nextToken: String): ModelTodoConnection
}

input ModelTodoConditionInput {
  content: ModelStringInput
  and: [ModelTodoConditionInput]
  or: [ModelTodoConditionInput]
  not: ModelTodoConditionInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
}

input CreateTodoInput {
  content: String
  id: ID
}

input UpdateTodoInput {
  content: String
  id: ID!
}

input DeleteTodoInput {
  id: ID!
}

type Mutation {
  createTodo(input: CreateTodoInput!, condition: ModelTodoConditionInput): Todo
  updateTodo(input: UpdateTodoInput!, condition: ModelTodoConditionInput): Todo
  deleteTodo(input: DeleteTodoInput!, condition: ModelTodoConditionInput): Todo
}

input ModelSubscriptionTodoFilterInput {
  content: ModelSubscriptionStringInput
  id: ModelSubscriptionIDInput
  createdAt: ModelSubscriptionStringInput
  updatedAt: ModelSubscriptionStringInput
  and: [ModelSubscriptionTodoFilterInput]
  or: [ModelSubscriptionTodoFilterInput]
}

type Subscription {
  onCreateTodo(filter: ModelSubscriptionTodoFilterInput): Todo @aws_subscribe(mutations: ["createTodo"])
  onUpdateTodo(filter: ModelSubscriptionTodoFilterInput): Todo @aws_subscribe(mutations: ["updateTodo"])
  onDeleteTodo(filter: ModelSubscriptionTodoFilterInput): Todo @aws_subscribe(mutations: ["deleteTodo"])
}

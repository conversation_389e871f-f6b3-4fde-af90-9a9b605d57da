{"version": 1, "models": {"Todo": {"name": "Todo", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "content": {"name": "content", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Todos", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "iam", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {}, "nonModels": {}}
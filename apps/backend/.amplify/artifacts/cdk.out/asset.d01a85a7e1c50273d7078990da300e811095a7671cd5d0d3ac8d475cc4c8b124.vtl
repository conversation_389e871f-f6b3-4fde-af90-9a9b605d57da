## [Start] Authorization Steps. **
$util.qr($ctx.stash.put("hasAuth", true))
#set( $isAuthorized = false )
#if( $util.authType() == "IAM Authorization" )
  #if( $util.authType() == "IAM Authorization" && $util.isNull($ctx.identity.cognitoIdentityPoolId) && $util.isNull($ctx.identity.cognitoIdentityId) )
    $util.qr($ctx.stash.put("hasAuth", true))
    #set( $isAuthorized = true )
  #else
    #if( !$isAuthorized )
      #if( $ctx.identity.userArn == $ctx.stash.unauthRole )
        #set( $isAuthorized = true )
      #end
    #end
  #end
#end
#if( $util.authType() == "User Pool Authorization" )
  #set( $hasValidOwnerArgument = false )
  #set( $authRuntimeFilter = [] )
  #set( $authOwnerRuntimeFilter = [] )
  #set( $authGroupRuntimeFilter = [] )
  ## Apply dynamic roles auth if not previously authorized by static groups and owner argument **
  #if( $authOwnerRuntimeFilter.size() > 0 )
    $util.qr($authRuntimeFilter.addAll($authOwnerRuntimeFilter))
  #end
  #if( $authGroupRuntimeFilter.size() > 0 )
    $util.qr($authRuntimeFilter.addAll($authGroupRuntimeFilter))
  #end
  #set( $filterArgsSize = 0 )
  #if( !$util.isNullOrEmpty($ctx.args.filter) )
    #set( $filterArgsSize = $ctx.args.filter.size() )
  #end
  #set( $isOwnerAuthAuthorizedAndNoOtherFilters = $hasValidOwnerArgument && $authRuntimeFilter.size() == 1 && $filterArgsSize == 0 )
  #set( $isOwnerOrDynamicAuthAuthorizedWithFilters = (!$isAuthorized || $hasValidOwnerArgument) && $authRuntimeFilter.size() > 0 )
  #if( !$isOwnerAuthAuthorizedAndNoOtherFilters && $isOwnerOrDynamicAuthAuthorizedWithFilters )
    #if( $util.isNullOrEmpty($ctx.args.filter) )
      #set( $ctx.args.filter = { "or": $authRuntimeFilter } )
    #else
      #set( $ctx.args.filter = { "and": [ { "or": $authRuntimeFilter }, $ctx.args.filter ]} )
    #end
    #set( $isAuthorized = true )
  #end
#end
#if( !$isAuthorized )
$util.unauthorized()
#end
$util.toJson({"version":"2018-05-29","payload":{}})
## [End] Authorization Steps. **
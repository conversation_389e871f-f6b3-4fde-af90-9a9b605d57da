{"version": "41.0.0", "artifacts": {"amplify-backend-oleksiistupak-sandbox-76f5473ec4.assets": {"type": "cdk:asset-manifest", "properties": {"file": "amplify-backend-oleksiistupak-sandbox-76f5473ec4.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "amplify-backend-oleksiistupak-sandbox-76f5473ec4": {"type": "aws:cloudformation:stack", "environment": "aws://unknown-account/unknown-region", "properties": {"templateFile": "amplify-backend-oleksiistupak-sandbox-76f5473ec4.template.json", "terminationProtection": false, "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-deploy-role-${AWS::AccountId}-${AWS::Region}", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-cfn-exec-role-${AWS::AccountId}-${AWS::Region}", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e23ed7222c04cde54f7c0da9c3d69180364bcf3455ccffc82028c5ade8fdfece.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["amplify-backend-oleksiistupak-sandbox-76f5473ec4.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-lookup-role-${AWS::AccountId}-${AWS::Region}", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["amplify-backend-oleksiistupak-sandbox-76f5473ec4.assets"], "metadata": {"/amplify-backend-oleksiistupak-sandbox-76f5473ec4": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}]}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/deploymentType": [{"type": "aws:cdk:logicalId", "data": "deploymentType"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/region": [{"type": "aws:cdk:logicalId", "data": "region"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPool/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthUserPool4BA7F805"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPoolAppClient/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthUserPoolAppClient2626C6F8"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPool": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthIdentityPool3FDE84CC"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/authenticatedUserRole/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthauthenticatedUserRoleD8DA3689"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/unauthenticatedUserRole/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthunauthenticatedUserRole2B524D9E"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPoolRoleAttachment": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthIdentityPoolRoleAttachment045F17C8"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth.NestedStack/auth.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "auth179371D7"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/userPoolId": [{"type": "aws:cdk:logicalId", "data": "userPoolId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/webClientId": [{"type": "aws:cdk:logicalId", "data": "webClientId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/identityPoolId": [{"type": "aws:cdk:logicalId", "data": "identityPoolId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/authRegion": [{"type": "aws:cdk:logicalId", "data": "authRegion"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/allowUnauthenticatedIdentities": [{"type": "aws:cdk:logicalId", "data": "allowUnauthenticatedIdentities"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/signupAttributes": [{"type": "aws:cdk:logicalId", "data": "signupAttributes"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/usernameAttributes": [{"type": "aws:cdk:logicalId", "data": "usernameAttributes"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/verificationMechanisms": [{"type": "aws:cdk:logicalId", "data": "verificationMechanisms"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/passwordPolicyMinLength": [{"type": "aws:cdk:logicalId", "data": "passwordPolicyMinLength"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/passwordPolicyRequirements": [{"type": "aws:cdk:logicalId", "data": "passwordPolicyRequirements"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/mfaConfiguration": [{"type": "aws:cdk:logicalId", "data": "mfaConfiguration"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/mfaTypes": [{"type": "aws:cdk:logicalId", "data": "mfaTypes"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/socialProviders": [{"type": "aws:cdk:logicalId", "data": "socialProviders"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthCognitoDomain": [{"type": "aws:cdk:logicalId", "data": "oauthCognitoDomain"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthScope": [{"type": "aws:cdk:logicalId", "data": "oauthScope"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthRedirectSignIn": [{"type": "aws:cdk:logicalId", "data": "oauthRedirectSignIn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthRedirectSignOut": [{"type": "aws:cdk:logicalId", "data": "oauthRedirectSignOut"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthResponseType": [{"type": "aws:cdk:logicalId", "data": "oauthResponseType"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthClientId": [{"type": "aws:cdk:logicalId", "data": "oauthClientId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/groups": [{"type": "aws:cdk:logicalId", "data": "groups"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPI42A6FA33"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/TransformerSchema": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPITransformerSchemaFF50A789"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/NONE_DS": [{"type": "graphqltransformer:resourceName", "data": "NONE_DS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/NONE_DS/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPINONEDS684BF699"}, {"type": "graphqltransformer:resourceName", "data": "NONE_DS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableIsCompleteRoleF825222C"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableOnEventRoleB4E71DEA"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Resource": [{"type": "aws:cdk:logicalId", "data": "TableManagerCustomProviderframeworkonEvent1DFC2ECC"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete/Resource": [{"type": "aws:cdk:logicalId", "data": "TableManagerCustomProviderframeworkisComplete2E51021B"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachineRole470BE899"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachine060600BC"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/HasEnvironmentParameter": [{"type": "aws:cdk:logicalId", "data": "HasEnvironmentParameter"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/ShouldUsePayPerRequestBilling": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePayPerRequestBilling"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/ShouldUsePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePointInTimeRecovery"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoTable": [{"type": "graphqltransformer:resourceName", "data": "Todo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoTable/Default/Default": [{"type": "aws:cdk:logicalId", "data": "TodoTable"}, {"type": "graphqltransformer:resourceName", "data": "Todo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CustomTableTodoTable": [{"type": "graphqltransformer:resourceName", "data": "Todo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/GetAttTodoTableStreamArn": [{"type": "aws:cdk:logicalId", "data": "GetAttTodoTableStreamArn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/GetAttTodoTableName": [{"type": "aws:cdk:logicalId", "data": "GetAttTodoTableName"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoIAMRole": [{"type": "graphqltransformer:resourceName", "data": "TodoIAMRole"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoIAMRole/Resource": [{"type": "aws:cdk:logicalId", "data": "TodoIAMRole2DA8E66E"}, {"type": "graphqltransformer:resourceName", "data": "TodoIAMRole"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoDataSource": [{"type": "graphqltransformer:resourceName", "data": "TodoTable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoDataSource/Resource": [{"type": "aws:cdk:logicalId", "data": "TodoDataSource"}, {"type": "graphqltransformer:resourceName", "data": "TodoTable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function/QuerygetTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetTodoauth0FunctionQuerygetTodoauth0FunctionAppSyncFunction846D8436"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function/QuerygetTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetTodopostAuth0FunctionQuerygetTodopostAuth0FunctionAppSyncFunction6BE14593"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/QueryGetTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryGetTodoDataResolverFnQueryGetTodoDataResolverFnAppSyncFunctionE2B57DAD"}, {"type": "graphqltransformer:resourceName", "data": "QueryGetTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/queryGetTodoResolver": [{"type": "aws:cdk:logicalId", "data": "GetTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.getTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function/QuerylistTodosauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistTodosauth0FunctionQuerylistTodosauth0FunctionAppSyncFunction7D761961"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistTodosauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function/QuerylistTodospostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistTodospostAuth0FunctionQuerylistTodospostAuth0FunctionAppSyncFunction154D8577"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistTodospostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/QueryListTodosDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryListTodosDataResolverFnQueryListTodosDataResolverFnAppSyncFunctionF825FE47"}, {"type": "graphqltransformer:resourceName", "data": "QueryListTodosDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/queryListTodosResolver": [{"type": "aws:cdk:logicalId", "data": "ListTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.listTodos"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function/MutationcreateTodoinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateTodoinit0FunctionMutationcreateTodoinit0FunctionAppSyncFunction54DE5B8B"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateTodoinit0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function/MutationcreateTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateTodoauth0FunctionMutationcreateTodoauth0FunctionAppSyncFunction21817E36"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function/MutationcreateTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateTodopostAuth0FunctionMutationcreateTodopostAuth0FunctionAppSyncFunctionED59EB9F"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/MutationCreateTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationCreateTodoDataResolverFnMutationCreateTodoDataResolverFnAppSyncFunction900EC5CF"}, {"type": "graphqltransformer:resourceName", "data": "MutationCreateTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationCreateTodoResolver": [{"type": "aws:cdk:logicalId", "data": "CreateTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.createTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function/MutationupdateTodoinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateTodoinit0FunctionMutationupdateTodoinit0FunctionAppSyncFunction1B95BB19"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateTodoinit0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/MutationupdateTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateTodoauth0FunctionMutationupdateTodoauth0FunctionAppSyncFunction1E4A3112"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function/MutationupdateTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateTodopostAuth0FunctionMutationupdateTodopostAuth0FunctionAppSyncFunction50C507D7"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/MutationUpdateTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationUpdateTodoDataResolverFnMutationUpdateTodoDataResolverFnAppSyncFunctionBC238C49"}, {"type": "graphqltransformer:resourceName", "data": "MutationUpdateTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationUpdateTodoResolver": [{"type": "aws:cdk:logicalId", "data": "UpdateTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.updateTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/MutationdeleteTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteTodoauth0FunctionMutationdeleteTodoauth0FunctionAppSyncFunctionC82C218C"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function/MutationdeleteTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteTodopostAuth0FunctionMutationdeleteTodopostAuth0FunctionAppSyncFunction483271A2"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/MutationDeleteTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationDeleteTodoDataResolverFnMutationDeleteTodoDataResolverFnAppSyncFunction3879153F"}, {"type": "graphqltransformer:resourceName", "data": "MutationDeleteTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationDeleteTodoResolver": [{"type": "aws:cdk:logicalId", "data": "DeleteTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.deleteTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function/SubscriptiononCreateTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateTodoauth0FunctionSubscriptiononCreateTodoauth0FunctionAppSyncFunction042EF9E1"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function/SubscriptiononCreateTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateTodopostAuth0FunctionSubscriptiononCreateTodopostAuth0FunctionAppSyncFunction18E34C4E"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/SubscriptionOnCreateTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnCreateTodoDataResolverFnSubscriptionOnCreateTodoDataResolverFnAppSyncFunction462A70C9"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnCreateTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnCreateTodoResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onCreateTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function/SubscriptiononUpdateTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateTodoauth0FunctionSubscriptiononUpdateTodoauth0FunctionAppSyncFunction80D0DFA3"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function/SubscriptiononUpdateTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateTodopostAuth0FunctionSubscriptiononUpdateTodopostAuth0FunctionAppSyncFunction04445BC7"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/SubscriptionOnUpdateTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnUpdateTodoDataResolverFnSubscriptionOnUpdateTodoDataResolverFnAppSyncFunction523DF0E4"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnUpdateTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnUpdateTodoResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onUpdateTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function/SubscriptiononDeleteTodoauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteTodoauth0FunctionSubscriptiononDeleteTodoauth0FunctionAppSyncFunctionF69F2220"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteTodoauth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function/SubscriptiononDeleteTodopostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteTodopostAuth0FunctionSubscriptiononDeleteTodopostAuth0FunctionAppSyncFunctionE131CBE7"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteTodopostAuth0Function"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/SubscriptionOnDeleteTodoDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnDeleteTodoDataResolverFnSubscriptionOnDeleteTodoDataResolverFnAppSyncFunction3E641E8C"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnDeleteTodoDataResolverFn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnDeleteTodoResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteTodoResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onDeleteTodo"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputsamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableMana3FE0897F"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo.NestedStack/Todo.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataTodoNestedStackTodoNestedStackResource551CEA56"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AuthRolePolicy01/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAuthRolePolicy01567A5654"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/UnauthRolePolicy01/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataUnauthRolePolicy01355B9DCF"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketAutoDeleteObjectsCustomResource437F26F5"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/LatestNodeRuntimeMap": [{"type": "aws:cdk:logicalId", "data": "LatestNodeRuntimeMap"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider": [{"type": "aws:cdk:is-custom-resource-handler-customResourceProvider", "data": true}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB": [{"type": "aws:cdk:is-custom-resource-handler-singleton", "data": true}, {"type": "aws:cdk:is-custom-resource-handler-runtime-family", "data": 2}, {"type": "aws:cdk:is-custom-resource-handler-singleton", "data": true}, {"type": "aws:cdk:is-custom-resource-handler-runtime-family", "data": 2}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketF566B665"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketPolicy4DAB0D15"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketAutoDeleteObjectsCustomResourceFE57309F"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketDeploymentAwsCliLayer13C432F7"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketDeploymentCustomResource1536MiB104B97EC"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAGRAPHQLENDPOINTParameter1C2CBB16"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAMODELINTROSPECTIONSCHEMABUCKETNAMEParameter47BF4F44"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAMODELINTROSPECTIONSCHEMAKEYParameterB6AEAE8A"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATADEFAULTNAMEParameterE7C23CC4"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn": [{"type": "aws:cdk:logicalId", "data": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/data.NestedStack/data.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "data7552DF31"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncApiId": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncApiId"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncApiEndpoint": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncApiEndpoint"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncAuthenticationType": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncAuthenticationType"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncRegion": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncRegion"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/amplifyApiModelSchemaS3Uri": [{"type": "aws:cdk:logicalId", "data": "amplifyApiModelSchemaS3Uri"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncAdditionalAuthenticationTypes": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncAdditionalAuthenticationTypes"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/amplify-backend-oleksiistupak-sandbox-76f5473ec4/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "amplify-backend-oleksiistupak-sandbox-76f5473ec4"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1005.0"}
{"version": "tree-0.1", "tree": {"id": "App", "path": "", "children": {"amplify-backend-oleksiistupak-sandbox-76f5473ec4": {"id": "amplify-backend-oleksiistupak-sandbox-76f5473ec4", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4", "children": {"deploymentType": {"id": "deploymentType", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/deploymentType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "region": {"id": "region", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/region", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "auth": {"id": "auth", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth", "children": {"amplifyAuth": {"id": "amplifyAuth", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth", "children": {"UserPool": {"id": "UserPool", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPool", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPool/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPool", "aws:cdk:cloudformation:props": {"accountRecoverySetting": {"recoveryMechanisms": [{"name": "verified_email", "priority": 1}]}, "adminCreateUserConfig": {"allowAdminCreateUserOnly": false}, "autoVerifiedAttributes": ["email"], "emailVerificationMessage": "The verification code to your new account is {####}", "emailVerificationSubject": "Verify your new account", "policies": {"passwordPolicy": {"minimumLength": 8, "requireLowercase": true, "requireUppercase": true, "requireNumbers": true, "requireSymbols": true}}, "schema": [{"name": "email", "mutable": true, "required": true}], "smsVerificationMessage": "The verification code to your new account is {####}", "userPoolTags": {"amplify:deployment-type": "sandbox", "amplify:friendly-name": "amplifyAuth", "created-by": "amplify"}, "userAttributeUpdateSettings": {"attributesRequireVerificationBeforeUpdate": ["email"]}, "usernameAttributes": ["email"], "usernameConfiguration": {"caseSensitive": false}, "verificationMessageTemplate": {"defaultEmailOption": "CONFIRM_WITH_CODE", "emailMessage": "The verification code to your new account is {####}", "emailSubject": "Verify your new account", "smsMessage": "The verification code to your new account is {####}"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPool", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPool", "version": "2.189.1", "metadata": []}}, "UserPoolAppClient": {"id": "UserPoolAppClient", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPoolAppClient", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/UserPoolAppClient/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPoolClient", "aws:cdk:cloudformation:props": {"allowedOAuthFlows": ["code"], "allowedOAuthFlowsUserPoolClient": true, "allowedOAuthScopes": ["profile", "phone", "email", "openid", "aws.cognito.signin.user.admin"], "callbackUrLs": ["https://example.com"], "explicitAuthFlows": ["ALLOW_CUSTOM_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"], "preventUserExistenceErrors": "ENABLED", "supportedIdentityProviders": ["COGNITO"], "userPoolId": {"Ref": "amplifyAuthUserPool4BA7F805"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPoolClient", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPoolClient", "version": "2.189.1", "metadata": []}}, "IdentityPool": {"id": "IdentityPool", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPool", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::IdentityPool", "aws:cdk:cloudformation:props": {"allowUnauthenticatedIdentities": true, "identityPoolTags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}], "cognitoIdentityProviders": [{"clientId": {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}, "providerName": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}]]}}], "supportedLoginProviders": {}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnIdentityPool", "version": "2.189.1"}}, "authenticatedUserRole": {"id": "authenticatedUserRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/authenticatedUserRole", "children": {"ImportauthenticatedUserRole": {"id": "ImportauthenticatedUserRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/authenticatedUserRole/ImportauthenticatedUserRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/authenticatedUserRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "authenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "unauthenticatedUserRole": {"id": "unauthenticatedUserRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/unauthenticatedUserRole", "children": {"ImportunauthenticatedUserRole": {"id": "ImportunauthenticatedUserRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/unauthenticatedUserRole/ImportunauthenticatedUserRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/unauthenticatedUserRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "unauthenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "IdentityPoolRoleAttachment": {"id": "IdentityPoolRoleAttachment", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifyAuth/IdentityPoolRoleAttachment", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::IdentityPoolRoleAttachment", "aws:cdk:cloudformation:props": {"identityPoolId": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}, "roleMappings": {"UserPoolWebClientRoleMapping": {"type": "Token", "ambiguousRoleResolution": "AuthenticatedRole", "identityProvider": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}, ":", {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}]]}}}, "roles": {"unauthenticated": {"Fn::GetAtt": ["amplifyAuthunauthenticatedUserRole2B524D9E", "<PERSON><PERSON>"]}, "authenticated": {"Fn::GetAtt": ["amplifyAuthauthenticatedUserRoleD8DA3689", "<PERSON><PERSON>"]}}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnIdentityPoolRoleAttachment", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPoolAppClient21E8C307Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth/amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "auth.NestedStack": {"id": "auth.<PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth.NestedStack", "children": {"auth.NestedStackResource": {"id": "auth.NestedStackResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/auth.NestedStack/auth.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/f929ef4a2a55bb95cd3541d12fb0df824b096a26b2e344744338a56bea560c37.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "userPoolId": {"id": "userPoolId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/userPoolId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "webClientId": {"id": "webClientId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/webClientId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "identityPoolId": {"id": "identityPoolId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/identityPoolId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "authRegion": {"id": "authRegion", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/authRegion", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "allowUnauthenticatedIdentities": {"id": "allowUnauthenticatedIdentities", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/allowUnauthenticatedIdentities", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "signupAttributes": {"id": "signupAttributes", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/signupAttributes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "usernameAttributes": {"id": "usernameAttributes", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/usernameAttributes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "verificationMechanisms": {"id": "verificationMechanisms", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/verificationMechanisms", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "passwordPolicyMinLength": {"id": "passwordPolicyMinLength", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/passwordPolicyMinLength", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "passwordPolicyRequirements": {"id": "passwordPolicyRequirements", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/passwordPolicyRequirements", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "mfaConfiguration": {"id": "mfaConfiguration", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/mfaConfiguration", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "mfaTypes": {"id": "mfaTypes", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/mfaTypes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "socialProviders": {"id": "socialProviders", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/socialProviders", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthCognitoDomain": {"id": "oauthCognitoDomain", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthCognitoDomain", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthScope": {"id": "oauthScope", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthScope", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthRedirectSignIn": {"id": "oauthRedirectSignIn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthRedirectSignIn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthRedirectSignOut": {"id": "oauthRedirectSignOut", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthRedirectSignOut", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthResponseType": {"id": "oauthResponseType", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthResponseType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthClientId": {"id": "oauthClientId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/oauthClientId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "groups": {"id": "groups", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/groups", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "data": {"id": "data", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data", "children": {"amplifyData": {"id": "amplifyData", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData", "children": {"transformer-user-pool": {"id": "transformer-user-pool", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/transformer-user-pool", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "GraphQLAPI": {"id": "GraphQLAPI", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::GraphQLApi", "aws:cdk:cloudformation:props": {"additionalAuthenticationProviders": [{"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"}, "awsRegion": {"Ref": "AWS::Region"}, "defaultAction": "ALLOW"}}], "authenticationType": "AWS_IAM", "name": "amplifyData", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnGraphQLApi", "version": "2.189.1"}}, "TransformerSchema": {"id": "TransformerSchema", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/TransformerSchema", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::GraphQLSchema", "aws:cdk:cloudformation:props": {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "definitionS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/5d06176bcc3e592a227be94f877d286eb82f30e2066d478a8eeb091baf786836.graphql"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnGraphQLSchema", "version": "2.189.1"}}, "NONE_DS": {"id": "NONE_DS", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/NONE_DS", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/NONE_DS/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "description": "None Data Source for Pipeline functions", "name": "NONE_DS", "type": "NONE"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.NoneDataSource", "version": "2.189.1"}}, "schema": {"id": "schema", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/schema", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/schema/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/GraphQLAPI/schema/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.GraphqlApiBase", "version": "2.189.1", "metadata": []}}, "AmplifyTableManager": {"id": "AmplifyTableManager", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager", "children": {"AmplifyManagedTableIsCompleteRole": {"id": "AmplifyManagedTableIsCompleteRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole", "children": {"ImportAmplifyManagedTableIsCompleteRole": {"id": "ImportAmplifyManagedTableIsCompleteRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/ImportAmplifyManagedTableIsCompleteRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "policies": [{"policyName": "CreateUpdateDeleteTablesPolicy", "policyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleAmplifyManagedTableIsCompleteRole": {"id": "ImmutableRoleAmplifyManagedTableIsCompleteRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/ImmutableRoleAmplifyManagedTableIsCompleteRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "AmplifyManagedTableOnEventRole": {"id": "AmplifyManagedTableOnEventRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole", "children": {"ImportAmplifyManagedTableOnEventRole": {"id": "ImportAmplifyManagedTableOnEventRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/ImportAmplifyManagedTableOnEventRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "policies": [{"policyName": "CreateUpdateDeleteTablesPolicy", "policyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "states:StartExecution", "Effect": "Allow", "Resource": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}], "Version": "2012-10-17"}, "policyName": "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6", "roles": [{"Ref": "AmplifyManagedTableOnEventRoleB4E71DEA"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "TableManagerCustomProvider": {"id": "TableManagerCustomProvider", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider", "children": {"framework-onEvent": {"id": "framework-onEvent", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent", "children": {"Code": {"id": "Code", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "description": "AmplifyManagedTable - onEvent (amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "environment": {"variables": {"WAITER_STATE_MACHINE_ARN": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}}, "handler": "amplify-table-manager-handler.onEvent", "role": {"Fn::GetAtt": ["AmplifyManagedTableOnEventRoleB4E71DEA", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "timeout": 840}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}, "framework-isComplete": {"id": "framework-isComplete", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "description": "AmplifyManagedTable - isComplete (amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "handler": "amplify-table-manager-handler.isComplete", "role": {"Fn::GetAtt": ["AmplifyManagedTableIsCompleteRoleF825222C", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "timeout": 840}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "AmplifyTableWaiterStateMachine": {"id": "AmplifyTableWaiterStateMachine", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine", "children": {"Role": {"id": "Role", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role", "children": {"ImportRole": {"id": "ImportRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/ImportRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, ":*"]]}]}], "Version": "2012-10-17"}, "policyName": "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A", "roles": [{"Ref": "AmplifyTableWaiterStateMachineRole470BE899"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "AmplifyTableManager.NestedStack": {"id": "AmplifyTableManager.NestedStack", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager.NestedStack", "children": {"AmplifyTableManager.NestedStackResource": {"id": "AmplifyTableManager.NestedStackResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/3c9bb4e8e9f3c54bb81bc0f237483dfcca03b0cb04612c1a2d76dde770e7a72d.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Todo": {"id": "Todo", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo", "children": {"DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "HasEnvironmentParameter": {"id": "HasEnvironmentParameter", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/HasEnvironmentParameter", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePayPerRequestBilling": {"id": "ShouldUsePayPerRequestBilling", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/ShouldUsePayPerRequestBilling", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePointInTimeRecovery": {"id": "ShouldUsePointInTimeRecovery", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/ShouldUsePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "TodoTable": {"id": "TodoTable", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoTable", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoTable/Default", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoTable/Default/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "CustomTableTodoTable": {"id": "CustomTableTodoTable", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CustomTableTodoTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.TableBase", "version": "2.189.1", "metadata": []}}, "GetAttTodoTableStreamArn": {"id": "GetAttTodoTableStreamArn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/GetAttTodoTableStreamArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "GetAttTodoTableName": {"id": "GetAttTodoTableName", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/GetAttTodoTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "TodoIAMRole": {"id": "TodoIAMRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoIAMRole", "children": {"ImportTodoIAMRole": {"id": "ImportTodoIAMRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoIAMRole/ImportTodoIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoIAMRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "policies": [{"policyName": "DynamoDBAccess", "policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["Todo-", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["Todo-", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}}], "roleName": {"Fn::Join": ["", ["TodoIAMRolecfd440-", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "-NONE"]]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleTodoIAMRole": {"id": "ImmutableRoleTodoIAMRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/ImmutableRoleTodoIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "TodoDataSource": {"id": "TodoDataSource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoDataSource", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/TodoDataSource/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dynamoDbConfig": {"tableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "awsRegion": {"Ref": "AWS::Region"}}, "name": "TodoTable", "serviceRoleArn": {"Fn::GetAtt": ["TodoIAMRole2DA8E66E", "<PERSON><PERSON>"]}, "type": "AMAZON_DYNAMODB"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.DynamoDbDataSource", "version": "2.189.1"}}, "QuerygetTodoauth0Function": {"id": "QuerygetTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function", "children": {"Templateresolvers--Query.getTodo.auth.1.req.vtl": {"id": "Templateresolvers--Query.getTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function/Templateresolvers--Query.getTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function/Templateresolvers--Query.getTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function/Templateresolvers--Query.getTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetTodoauth0Function.AppSyncFunction": {"id": "QuerygetTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodoauth0Function/QuerygetTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "QuerygetTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e7b2a5c2142d48e60eebe8e4beed7cc465c87e36229199889878edae668ef132.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerygetTodopostAuth0Function": {"id": "QuerygetTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function", "children": {"Templateresolvers--Query.getTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.getTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function/Templateresolvers--Query.getTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function/Templateresolvers--Query.getTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function/Templateresolvers--Query.getTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetTodopostAuth0Function.AppSyncFunction": {"id": "QuerygetTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerygetTodopostAuth0Function/QuerygetTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "QuerygetTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryGetTodoDataResolverFn": {"id": "QueryGetTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn", "children": {"Templateresolvers--Query.getTodo.req.vtl": {"id": "Templateresolvers--Query.getTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.getTodo.res.vtl": {"id": "Templateresolvers--Query.getTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/Templateresolvers--Query.getTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryGetTodoDataResolverFn.AppSyncFunction": {"id": "QueryGetTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryGetTodoDataResolverFn/QueryGetTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryGetTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryGetTodoResolver": {"id": "queryGetTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/queryGetTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "getTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerygetTodoauth0FunctionQuerygetTodoauth0FunctionAppSyncFunction846D8436", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetTodopostAuth0FunctionQuerygetTodopostAuth0FunctionAppSyncFunction6BE14593", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetTodoDataResolverFnQueryGetTodoDataResolverFnAppSyncFunctionE2B57DAD", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "QuerylistTodosauth0Function": {"id": "QuerylistTodosauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function", "children": {"Templateresolvers--Query.listTodos.auth.1.req.vtl": {"id": "Templateresolvers--Query.listTodos.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function/Templateresolvers--Query.listTodos.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function/Templateresolvers--Query.listTodos.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function/Templateresolvers--Query.listTodos.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistTodosauth0Function.AppSyncFunction": {"id": "QuerylistTodosauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodosauth0Function/QuerylistTodosauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "QuerylistTodosauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e7b2a5c2142d48e60eebe8e4beed7cc465c87e36229199889878edae668ef132.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerylistTodospostAuth0Function": {"id": "QuerylistTodospostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function", "children": {"Templateresolvers--Query.listTodos.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.listTodos.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function/Templateresolvers--Query.listTodos.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function/Templateresolvers--Query.listTodos.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function/Templateresolvers--Query.listTodos.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistTodospostAuth0Function.AppSyncFunction": {"id": "QuerylistTodospostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QuerylistTodospostAuth0Function/QuerylistTodospostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "QuerylistTodospostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryListTodosDataResolverFn": {"id": "QueryListTodosDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn", "children": {"Templateresolvers--Query.listTodos.req.vtl": {"id": "Templateresolvers--Query.listTodos.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.listTodos.res.vtl": {"id": "Templateresolvers--Query.listTodos.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/Templateresolvers--Query.listTodos.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryListTodosDataResolverFn.AppSyncFunction": {"id": "QueryListTodosDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/QueryListTodosDataResolverFn/QueryListTodosDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryListTodosDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryListTodosResolver": {"id": "queryListTodosResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/queryListTodosResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "listTodos", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerylistTodosauth0FunctionQuerylistTodosauth0FunctionAppSyncFunction7D761961", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistTodospostAuth0FunctionQuerylistTodospostAuth0FunctionAppSyncFunction154D8577", "FunctionId"]}, {"Fn::GetAtt": ["QueryListTodosDataResolverFnQueryListTodosDataResolverFnAppSyncFunctionF825FE47", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listTodos\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationcreateTodoinit0Function": {"id": "MutationcreateTodoinit0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function", "children": {"Templateresolvers--Mutation.createTodo.init.1.req.vtl": {"id": "Templateresolvers--Mutation.createTodo.init.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function/Templateresolvers--Mutation.createTodo.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function/Templateresolvers--Mutation.createTodo.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function/Templateresolvers--Mutation.createTodo.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateTodoinit0Function.AppSyncFunction": {"id": "MutationcreateTodoinit0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoinit0Function/MutationcreateTodoinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationcreateTodoinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateTodoauth0Function": {"id": "MutationcreateTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function", "children": {"Templateresolvers--Mutation.createTodo.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.createTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function/Templateresolvers--Mutation.createTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function/Templateresolvers--Mutation.createTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function/Templateresolvers--Mutation.createTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateTodoauth0Function.AppSyncFunction": {"id": "MutationcreateTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodoauth0Function/MutationcreateTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationcreateTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/687e5bb5b18cd04d8e695d1f85ad9d43df05210383aba8097d72910c47b3e7e1.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateTodopostAuth0Function": {"id": "MutationcreateTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function", "children": {"Templateresolvers--Mutation.createTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.createTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function/Templateresolvers--Mutation.createTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function/Templateresolvers--Mutation.createTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function/Templateresolvers--Mutation.createTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateTodopostAuth0Function.AppSyncFunction": {"id": "MutationcreateTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationcreateTodopostAuth0Function/MutationcreateTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationcreateTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationCreateTodoDataResolverFn": {"id": "MutationCreateTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn", "children": {"Templateresolvers--Mutation.createTodo.req.vtl": {"id": "Templateresolvers--Mutation.createTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.createTodo.res.vtl": {"id": "Templateresolvers--Mutation.createTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/Templateresolvers--Mutation.createTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationCreateTodoDataResolverFn.AppSyncFunction": {"id": "MutationCreateTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationCreateTodoDataResolverFn/MutationCreateTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationCreateTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/2bf64327ca5682da4be84d0d16440204e25abc3a221195b41f2d21dfa432e5ab.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationCreateTodoResolver": {"id": "mutationCreateTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationCreateTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "createTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationcreateTodoinit0FunctionMutationcreateTodoinit0FunctionAppSyncFunction54DE5B8B", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateTodoauth0FunctionMutationcreateTodoauth0FunctionAppSyncFunction21817E36", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateTodopostAuth0FunctionMutationcreateTodopostAuth0FunctionAppSyncFunctionED59EB9F", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateTodoDataResolverFnMutationCreateTodoDataResolverFnAppSyncFunction900EC5CF", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationupdateTodoinit0Function": {"id": "MutationupdateTodoinit0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function", "children": {"Templateresolvers--Mutation.updateTodo.init.1.req.vtl": {"id": "Templateresolvers--Mutation.updateTodo.init.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function/Templateresolvers--Mutation.updateTodo.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function/Templateresolvers--Mutation.updateTodo.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function/Templateresolvers--Mutation.updateTodo.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateTodoinit0Function.AppSyncFunction": {"id": "MutationupdateTodoinit0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoinit0Function/MutationupdateTodoinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationupdateTodoinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateTodoauth0Function": {"id": "MutationupdateTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function", "children": {"Templateresolvers--Mutation.updateTodo.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateTodo.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.updateTodo.auth.1.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/Templateresolvers--Mutation.updateTodo.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateTodoauth0Function.AppSyncFunction": {"id": "MutationupdateTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodoauth0Function/MutationupdateTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationupdateTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/035bca645c95ac13c5dbae712a5a00df2f0b40b2772e5077b865722057d0b2b7.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateTodopostAuth0Function": {"id": "MutationupdateTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function", "children": {"Templateresolvers--Mutation.updateTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function/Templateresolvers--Mutation.updateTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function/Templateresolvers--Mutation.updateTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function/Templateresolvers--Mutation.updateTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateTodopostAuth0Function.AppSyncFunction": {"id": "MutationupdateTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationupdateTodopostAuth0Function/MutationupdateTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationupdateTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationUpdateTodoDataResolverFn": {"id": "MutationUpdateTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn", "children": {"Templateresolvers--Mutation.updateTodo.req.vtl": {"id": "Templateresolvers--Mutation.updateTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateTodo.res.vtl": {"id": "Templateresolvers--Mutation.updateTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/Templateresolvers--Mutation.updateTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationUpdateTodoDataResolverFn.AppSyncFunction": {"id": "MutationUpdateTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationUpdateTodoDataResolverFn/MutationUpdateTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationUpdateTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationUpdateTodoResolver": {"id": "mutationUpdateTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationUpdateTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "updateTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationupdateTodoinit0FunctionMutationupdateTodoinit0FunctionAppSyncFunction1B95BB19", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateTodoauth0FunctionMutationupdateTodoauth0FunctionAppSyncFunction1E4A3112", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateTodopostAuth0FunctionMutationupdateTodopostAuth0FunctionAppSyncFunction50C507D7", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateTodoDataResolverFnMutationUpdateTodoDataResolverFnAppSyncFunctionBC238C49", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationdeleteTodoauth0Function": {"id": "MutationdeleteTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function", "children": {"Templateresolvers--Mutation.deleteTodo.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteTodo.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.deleteTodo.auth.1.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/Templateresolvers--Mutation.deleteTodo.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteTodoauth0Function.AppSyncFunction": {"id": "MutationdeleteTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodoauth0Function/MutationdeleteTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationdeleteTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/04afff6c870576131ba15da50cb5c3c0278fbab9d12c4fd63520a49832c46482.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationdeleteTodopostAuth0Function": {"id": "MutationdeleteTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function", "children": {"Templateresolvers--Mutation.deleteTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function/Templateresolvers--Mutation.deleteTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function/Templateresolvers--Mutation.deleteTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function/Templateresolvers--Mutation.deleteTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteTodopostAuth0Function.AppSyncFunction": {"id": "MutationdeleteTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationdeleteTodopostAuth0Function/MutationdeleteTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "MutationdeleteTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationDeleteTodoDataResolverFn": {"id": "MutationDeleteTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn", "children": {"Templateresolvers--Mutation.deleteTodo.req.vtl": {"id": "Templateresolvers--Mutation.deleteTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteTodo.res.vtl": {"id": "Templateresolvers--Mutation.deleteTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/Templateresolvers--Mutation.deleteTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationDeleteTodoDataResolverFn.AppSyncFunction": {"id": "MutationDeleteTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/MutationDeleteTodoDataResolverFn/MutationDeleteTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Fn::GetAtt": ["TodoDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationDeleteTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationDeleteTodoResolver": {"id": "mutationDeleteTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/mutationDeleteTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "deleteTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationdeleteTodoauth0FunctionMutationdeleteTodoauth0FunctionAppSyncFunctionC82C218C", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteTodopostAuth0FunctionMutationdeleteTodopostAuth0FunctionAppSyncFunction483271A2", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteTodoDataResolverFnMutationDeleteTodoDataResolverFnAppSyncFunction3879153F", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["TodoTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononCreateTodoauth0Function": {"id": "SubscriptiononCreateTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function", "children": {"Templateresolvers--Subscription.onCreateTodo.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function/Templateresolvers--Subscription.onCreateTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function/Templateresolvers--Subscription.onCreateTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function/Templateresolvers--Subscription.onCreateTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateTodoauth0Function.AppSyncFunction": {"id": "SubscriptiononCreateTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodoauth0Function/SubscriptiononCreateTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/d01a85a7e1c50273d7078990da300e811095a7671cd5d0d3ac8d475cc4c8b124.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononCreateTodopostAuth0Function": {"id": "SubscriptiononCreateTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function", "children": {"Templateresolvers--Subscription.onCreateTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function/Templateresolvers--Subscription.onCreateTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function/Templateresolvers--Subscription.onCreateTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function/Templateresolvers--Subscription.onCreateTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateTodopostAuth0Function.AppSyncFunction": {"id": "SubscriptiononCreateTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononCreateTodopostAuth0Function/SubscriptiononCreateTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnCreateTodoDataResolverFn": {"id": "SubscriptionOnCreateTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn", "children": {"Templateresolvers--Subscription.onCreateTodo.req.vtl": {"id": "Templateresolvers--Subscription.onCreateTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onCreateTodo.res.vtl": {"id": "Templateresolvers--Subscription.onCreateTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/Templateresolvers--Subscription.onCreateTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnCreateTodoDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnCreateTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnCreateTodoDataResolverFn/SubscriptionOnCreateTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnCreateTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnCreateTodoResolver": {"id": "subscriptionOnCreateTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnCreateTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "onCreateTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononCreateTodoauth0FunctionSubscriptiononCreateTodoauth0FunctionAppSyncFunction042EF9E1", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateTodopostAuth0FunctionSubscriptiononCreateTodopostAuth0FunctionAppSyncFunction18E34C4E", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateTodoDataResolverFnSubscriptionOnCreateTodoDataResolverFnAppSyncFunction462A70C9", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononUpdateTodoauth0Function": {"id": "SubscriptiononUpdateTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function", "children": {"Templateresolvers--Subscription.onUpdateTodo.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function/Templateresolvers--Subscription.onUpdateTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function/Templateresolvers--Subscription.onUpdateTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function/Templateresolvers--Subscription.onUpdateTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateTodoauth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodoauth0Function/SubscriptiononUpdateTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/d01a85a7e1c50273d7078990da300e811095a7671cd5d0d3ac8d475cc4c8b124.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononUpdateTodopostAuth0Function": {"id": "SubscriptiononUpdateTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function", "children": {"Templateresolvers--Subscription.onUpdateTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function/Templateresolvers--Subscription.onUpdateTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function/Templateresolvers--Subscription.onUpdateTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function/Templateresolvers--Subscription.onUpdateTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateTodopostAuth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononUpdateTodopostAuth0Function/SubscriptiononUpdateTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnUpdateTodoDataResolverFn": {"id": "SubscriptionOnUpdateTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn", "children": {"Templateresolvers--Subscription.onUpdateTodo.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onUpdateTodo.res.vtl": {"id": "Templateresolvers--Subscription.onUpdateTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/Templateresolvers--Subscription.onUpdateTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnUpdateTodoDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnUpdateTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnUpdateTodoDataResolverFn/SubscriptionOnUpdateTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnUpdateTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnUpdateTodoResolver": {"id": "subscriptionOnUpdateTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnUpdateTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "onUpdateTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononUpdateTodoauth0FunctionSubscriptiononUpdateTodoauth0FunctionAppSyncFunction80D0DFA3", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateTodopostAuth0FunctionSubscriptiononUpdateTodopostAuth0FunctionAppSyncFunction04445BC7", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateTodoDataResolverFnSubscriptionOnUpdateTodoDataResolverFnAppSyncFunction523DF0E4", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononDeleteTodoauth0Function": {"id": "SubscriptiononDeleteTodoauth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function", "children": {"Templateresolvers--Subscription.onDeleteTodo.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteTodo.auth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function/Templateresolvers--Subscription.onDeleteTodo.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function/Templateresolvers--Subscription.onDeleteTodo.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function/Templateresolvers--Subscription.onDeleteTodo.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteTodoauth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteTodoauth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodoauth0Function/SubscriptiononDeleteTodoauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteTodoauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/d01a85a7e1c50273d7078990da300e811095a7671cd5d0d3ac8d475cc4c8b124.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononDeleteTodopostAuth0Function": {"id": "SubscriptiononDeleteTodopostAuth0Function", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function", "children": {"Templateresolvers--Subscription.onDeleteTodo.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteTodo.postAuth.1.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function/Templateresolvers--Subscription.onDeleteTodo.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function/Templateresolvers--Subscription.onDeleteTodo.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function/Templateresolvers--Subscription.onDeleteTodo.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteTodopostAuth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteTodopostAuth0Function.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptiononDeleteTodopostAuth0Function/SubscriptiononDeleteTodopostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteTodopostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnDeleteTodoDataResolverFn": {"id": "SubscriptionOnDeleteTodoDataResolverFn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn", "children": {"Templateresolvers--Subscription.onDeleteTodo.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteTodo.req.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onDeleteTodo.res.vtl": {"id": "Templateresolvers--Subscription.onDeleteTodo.res.vtl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/Templateresolvers--Subscription.onDeleteTodo.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnDeleteTodoDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnDeleteTodoDataResolverFn.AppSyncFunction", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/SubscriptionOnDeleteTodoDataResolverFn/SubscriptionOnDeleteTodoDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "dataSourceName": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnDeleteTodoDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnDeleteTodoResolver": {"id": "subscriptionOnDeleteTodoResolver", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/subscriptionOnDeleteTodoResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "fieldName": "onDeleteTodo", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononDeleteTodoauth0FunctionSubscriptiononDeleteTodoauth0FunctionAppSyncFunctionF69F2220", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteTodopostAuth0FunctionSubscriptiononDeleteTodopostAuth0FunctionAppSyncFunctionE131CBE7", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteTodoDataResolverFnSubscriptionOnDeleteTodoDataResolverFnAppSyncFunction3E641E8C", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteTodo\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "Todo.NestedStack": {"id": "Todo.NestedStack", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo.NestedStack", "children": {"Todo.NestedStackResource": {"id": "Todo.NestedStackResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/Todo.NestedStack/Todo.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource7784B3EEOutputsamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableMana3FE0897F": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEvent9FD9AB99Arn"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPINONEDSC32D3C95Name": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/80161cf112a33442f6fa4d89279b689d0162b640ef57182576f71ee1042978e8.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "auth-role-name": {"id": "auth-role-name", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/auth-role-name", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "AuthRolePolicy01": {"id": "AuthRolePolicy01", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AuthRolePolicy01", "children": {"ImportedAuthRolePolicy01": {"id": "ImportedAuthRolePolicy01", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AuthRolePolicy01/ImportedAuthRolePolicy01", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AuthRolePolicy01/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::ManagedPolicy", "aws:cdk:cloudformation:props": {"description": "", "path": "/", "policyDocument": {"Statement": [{"Action": "appsync:GraphQL", "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/*", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Todo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "getTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "listTodos"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "createTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "updateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "deleteTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onCreateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onUpdateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onDeleteTodo"}]}]}], "Version": "2012-10-17"}, "roles": [{"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnManagedPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.ManagedPolicy", "version": "2.189.1", "metadata": []}}, "unauth-role-name": {"id": "unauth-role-name", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/unauth-role-name", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "UnauthRolePolicy01": {"id": "UnauthRolePolicy01", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/UnauthRolePolicy01", "children": {"ImportedUnauthRolePolicy01": {"id": "ImportedUnauthRolePolicy01", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/UnauthRolePolicy01/ImportedUnauthRolePolicy01", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/UnauthRolePolicy01/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::ManagedPolicy", "aws:cdk:cloudformation:props": {"description": "", "path": "/", "policyDocument": {"Statement": [{"Action": "appsync:GraphQL", "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/*", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Todo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "getTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Query", "fieldName": "listTodos"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "createTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "updateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Mutation", "fieldName": "deleteTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onCreateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onUpdateTodo"}]}, {"Fn::Sub": ["arn:${AWS::Partition}:appsync:${AWS::Region}:${AWS::AccountId}:apis/${apiId}/types/${typeName}/fields/${fieldName}", {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "typeName": "Subscription", "fieldName": "onDeleteTodo"}]}]}], "Version": "2012-10-17"}, "roles": [{"Ref": "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnManagedPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.ManagedPolicy", "version": "2.189.1", "metadata": []}}, "AmplifyCodegenAssets": {"id": "AmplifyCodegenAssets", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets", "children": {"AmplifyCodegenAssetsBucket": {"id": "AmplifyCodegenAssetsBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"corsConfiguration": {"corsRules": [{"allowedHeaders": ["*"], "allowedMethods": ["GET", "HEAD"], "allowedOrigins": [{"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/amplify"]]}]}]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "aws-cdk:auto-delete-objects", "value": "true"}, {"key": "aws-cdk:cr-owned:6628fe5e", "value": "true"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.189.1"}}, "Policy": {"id": "Policy", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "policyDocument": {"Statement": [{"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.189.1", "metadata": []}}, "AutoDeleteObjectsCustomResource": {"id": "AutoDeleteObjectsCustomResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.189.1", "metadata": []}}, "AmplifyCodegenAssetsDeployment": {"id": "AmplifyCodegenAssetsDeployment", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment", "children": {"AwsCliLayer": {"id": "AwsCli<PERSON>ayer", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer", "children": {"Code": {"id": "Code", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::LayerVersion", "aws:cdk:cloudformation:props": {"content": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip"}, "description": "/opt/awscli/aws"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnLayerVersion", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.lambda_layer_awscli.AwsCliLayer", "version": "2.189.1", "metadata": []}}, "CustomResourceHandler": {"id": "CustomResourceHandler", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResourceHandler", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.SingletonFunction", "version": "2.189.1", "metadata": []}}, "Asset1": {"id": "Asset1", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "CustomResource-1536MiB": {"id": "CustomResource-1536MiB", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}, "DestinationBucket": {"id": "DestinationBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/DestinationBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_deployment.BucketDeployment", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "L2GraphqlApi": {"id": "L2GraphqlApi", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifyData/L2GraphqlApi", "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.GraphqlApiBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "@aws-amplify/graphql-api-construct.AmplifyGraphqlApi", "version": "1.20.3"}}, "DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "LatestNodeRuntimeMap": {"id": "LatestNodeRuntimeMap", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/LatestNodeRuntimeMap", "constructInfo": {"fqn": "aws-cdk-lib.CfnMapping", "version": "2.189.1"}}, "Custom::S3AutoDeleteObjectsCustomResourceProvider": {"id": "Custom::S3AutoDeleteObjectsCustomResourceProvider", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider", "children": {"Staging": {"id": "Staging", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "Role": {"id": "Role", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Handler": {"id": "Handler", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProviderBase", "version": "2.189.1"}}, "Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB": {"id": "Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB", "children": {"ServiceRole": {"id": "ServiceRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole", "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}]]}, {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "policyName": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "roles": [{"Ref": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "Code": {"id": "Code", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f.zip"}, "environment": {"variables": {"AWS_CA_BUNDLE": "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"}}, "handler": "index.handler", "layers": [{"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "memorySize": 1536, "role": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2", "<PERSON><PERSON>"]}, "runtime": "python3.11", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "timeout": 900}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}, "modelIntrospectionSchemaBucket": {"id": "modelIntrospectionSchemaBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "aws-cdk:auto-delete-objects", "value": "true"}, {"key": "aws-cdk:cr-owned:a1ebbbad", "value": "true"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.189.1"}}, "Policy": {"id": "Policy", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Policy", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/Policy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "policyDocument": {"Statement": [{"Action": "s3:*", "Condition": {"Bool": {"aws:SecureTransport": "false"}}, "Effect": "<PERSON><PERSON>", "Principal": {"AWS": "*"}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.189.1", "metadata": []}}, "AutoDeleteObjectsCustomResource": {"id": "AutoDeleteObjectsCustomResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.189.1", "metadata": []}}, "modelIntrospectionSchemaBucketDeployment": {"id": "modelIntrospectionSchemaBucketDeployment", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment", "children": {"AwsCliLayer": {"id": "AwsCli<PERSON>ayer", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer", "children": {"Code": {"id": "Code", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::LayerVersion", "aws:cdk:cloudformation:props": {"content": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "c49d356cac773d491c5f7ac148995a1181498a8e289429f8612a7f7e3814f535.zip"}, "description": "/opt/awscli/aws"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnLayerVersion", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.lambda_layer_awscli.AwsCliLayer", "version": "2.189.1", "metadata": []}}, "CustomResourceHandler": {"id": "CustomResourceHandler", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/CustomResourceHandler", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.SingletonFunction", "version": "2.189.1", "metadata": []}}, "Asset1": {"id": "Asset1", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/Asset1", "children": {"Stage": {"id": "Stage", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/Asset1/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/Asset1/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "CustomResource-1536MiB": {"id": "CustomResource-1536MiB", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_deployment.BucketDeployment", "version": "2.189.1"}}, "AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter": {"id": "AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_GRAPHQL_ENDPOINT", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter": {"id": "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAME", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter": {"id": "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEY", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": "modelIntrospectionSchema.json"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_DEFAULT_NAMEParameter": {"id": "AMPLIFY_DATA_DEFAULT_NAMEParameter", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_DEFAULT_NAMEParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/backend/oleksiistupak-sandbox-76f5473ec4/AMPLIFY_DATA_DEFAULT_NAME", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": "amplifyData"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"id": "reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/reference-to-amplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataGraphQLAPIF6CEB3C7GraphQLUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn": {"id": "amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data/amplifybackendoleksiistupaksandbox76f5473ec4dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB186D4C65DestinationBucketArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "data.NestedStack": {"id": "data.NestedStack", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data.NestedStack", "children": {"data.NestedStackResource": {"id": "data.NestedStackResource", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/data.NestedStack/data.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthUserPool4F047AECRef"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthauthenticatedUserRole4F3ABC68Ref"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthunauthenticatedUserRoleB3275C10Ref"]}, "referencetoamplifybackendoleksiistupaksandbox76f5473ec4authNestedStackauthNestedStackResourceA8DEECF0Outputsamplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifybackendoleksiistupaksandbox76f5473ec4authamplifyAuthIdentityPoolF05A0FDBRef"]}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/fd82bae47151d806d4672f457cbc91f303ac948c35b2613e314faa47c24fd5e5.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "awsAppsyncApiId": {"id": "awsAppsyncApiId", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncApiEndpoint": {"id": "awsAppsyncApiEndpoint", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncApiEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncAuthenticationType": {"id": "awsAppsyncAuthenticationType", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncAuthenticationType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncRegion": {"id": "awsAppsyncRegion", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncRegion", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyApiModelSchemaS3Uri": {"id": "amplifyApiModelSchemaS3Uri", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/amplifyApiModelSchemaS3Uri", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncAdditionalAuthenticationTypes": {"id": "awsAppsyncAdditionalAuthenticationTypes", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/awsAppsyncAdditionalAuthenticationTypes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "amplify-backend-oleksiistupak-sandbox-76f5473ec4/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.189.1"}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.189.1"}}}
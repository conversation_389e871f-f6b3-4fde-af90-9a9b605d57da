{"auth": {"user_pool_id": "us-east-1_LP2ulIfEZ", "aws_region": "us-east-1", "user_pool_client_id": "19h12k4sh8o0f3305i4rvk349", "identity_pool_id": "us-east-1:3a87e091-3b6d-4593-99be-a1b60bb6f6ea", "mfa_methods": [], "standard_required_attributes": ["email"], "username_attributes": ["email"], "user_verification_types": ["email"], "groups": [], "mfa_configuration": "NONE", "password_policy": {"min_length": 8, "require_lowercase": true, "require_numbers": true, "require_symbols": true, "require_uppercase": true}, "unauthenticated_identities_enabled": true}, "data": {"url": "https://rmzdpoz4bjfxvcswkbyavzql6q.appsync-api.us-east-1.amazonaws.com/graphql", "aws_region": "us-east-1", "default_authorization_type": "AWS_IAM", "authorization_types": ["AMAZON_COGNITO_USER_POOLS"], "model_introspection": {"version": 1, "models": {"Todo": {"name": "Todo", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "content": {"name": "content", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Todos", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "iam", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {}, "nonModels": {}}}, "version": "1.4"}